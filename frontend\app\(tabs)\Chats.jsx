import { View, StyleSheet } from 'react-native';
import React, { useContext } from 'react';
import SearchBar from '../Components/Shared/SearchBar';
import Header from '../Components/Home/Header';
import Users from '../Components/Chats/Users';
import { ThemeContext } from '../../context/ThemeContext';

export default function Chats() {
    const { theme } = useContext(ThemeContext);
    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <View style={styles.fixedTop}>
                <Header />
                <SearchBar />
            </View>
            <View style={styles.scrollableList}>
                <Users />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    fixedTop: {
        paddingBottom: 8,
    },
    scrollableList: {
        flex: 1,
    },
});
