import React, { useState, useEffect, useContext } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    FlatList,
    Image,
    SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ThemeContext } from '../../../context/ThemeContext';

export default function Users() {
    const themeContext = useContext(ThemeContext);
    const theme = themeContext?.theme || {};
    const navigation = useNavigation();

    const users = React.useMemo(
        () => [
            {
                id: 1,
                name: 'Rohith',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'Hello',
                time: '10:00 AM',
                totalUnread: 2,
            },
            {
                id: 2,
                name: '<PERSON>rathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
            {
                id: 3,
                name: '<PERSON>rathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
            {
                id: 4,
                name: 'Prathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
            {
                id: 5,
                name: 'Prathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 4,
            },
            {
                id: 6,
                name: 'Prathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
            {
                id: 7,
                name: 'Prathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
            {
                id: 8,
                name: 'Prathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
            {
                id: 9,
                name: 'Prathik',
                profile: require('../../../assets/images/profile.png'),
                lastMessage: 'How are you?',
                time: '9:45 AM',
                totalUnread: 1,
            },
        ],
        []
    );

    const [chatData, setChatData] = useState([]);

    useEffect(() => {
        setChatData(users);
    }, [users]);

    const renderItem = ({ item }) => (
        <TouchableOpacity
            style={styles.chatContainer}
            onPress={() => navigation.navigate('ChatScreen', { user: item })}
        >
            <Image source={item.profile} style={styles.image} />
            <View style={styles.chatContent}>
                <View style={styles.chatHeader}>
                    <Text style={styles.chatName}>{item.name}</Text>
                    <Text style={styles.chatTime}>{item.time}</Text>
                </View>
                <View style={styles.messageRow}>
                    <Text style={styles.lastMessage} numberOfLines={1}>
                        {item.lastMessage}
                    </Text>
                    {item.totalUnread > 0 && (
                        <View
                            style={[
                                styles.unreadBadge,
                                { backgroundColor: theme.PRIMARY },
                            ]}
                        >
                            <Text style={styles.unreadText}>
                                {item.totalUnread}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <FlatList
                data={chatData}
                keyExtractor={(item) => item.id.toString()}
                renderItem={renderItem}
            />
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 3,
    },
    chatContainer: {
        flexDirection: 'row',
        padding: 16,
        alignItems: 'center',
        borderBottomWidth: 0.5,
        borderBottomColor: '#ccc',
    },
    image: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'gray',
    },
    chatContent: {
        flex: 1,
        marginLeft: 15,
    },
    chatHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    chatName: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    chatTime: {
        fontSize: 12,
        color: '#888',
    },
    messageRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 4,
        alignItems: 'center',
    },
    lastMessage: {
        fontSize: 14,
        color: '#444',
        flex: 1,
    },
    unreadBadge: {
        borderRadius: 10,
        paddingHorizontal: 6,
        paddingVertical: 2,
        marginLeft: 8,
    },
    unreadText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
    },
});
