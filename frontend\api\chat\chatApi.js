import axios from 'axios';
import io from 'socket.io-client';
import { privateAPIClient } from '../index';

const chatApi = axios.create({
    baseURL: `${privateAPIClient.defaults.baseURL}/api/chat`,
});

// Add auth token to requests
chatApi.interceptors.request.use((config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Socket.IO connection
let socket = null;

export const initializeSocket = (userId) => {
    if (socket) {
        socket.disconnect();
    }

    socket = io(`${privateAPIClient.defaults.baseURL}`, {
        auth: {
            token: localStorage.getItem('authToken'),
            userId: userId,
        },
        transports: ['websocket'],
    });

    return socket;
};

export const getSocket = () => socket;

export const disconnectSocket = () => {
    if (socket) {
        socket.disconnect();
        socket = null;
    }
};

// Chat room management
export const createChatRoom = async (
    participants,
    type = 'direct',
    metadata = {}
) => {
    const { data } = await chatApi.post('/rooms', {
        participants,
        type, // 'direct', 'group', 'land_discussion'
        metadata, // { landId, contractorId, etc. }
    });
    return data;
};

export const getChatRooms = async (userId) => {
    const { data } = await chatApi.get(`/rooms/user/${userId}`);
    return data;
};

export const getChatRoom = async (roomId) => {
    const { data } = await chatApi.get(`/rooms/${roomId}`);
    return data;
};

export const joinChatRoom = async (roomId) => {
    const { data } = await chatApi.post(`/rooms/${roomId}/join`);
    return data;
};

export const leaveChatRoom = async (roomId) => {
    const { data } = await chatApi.post(`/rooms/${roomId}/leave`);
    return data;
};

// Message management
export const getMessages = async (roomId, page = 1, limit = 50) => {
    const { data } = await chatApi.get(`/rooms/${roomId}/messages`, {
        params: { page, limit },
    });
    return data;
};

export const sendMessage = async (roomId, messageData) => {
    const formData = new FormData();

    formData.append('content', messageData.content || '');
    formData.append('type', messageData.type || 'text'); // 'text', 'image', 'file', 'location'

    if (messageData.file) {
        formData.append('file', messageData.file);
    }

    if (messageData.location) {
        formData.append('location', JSON.stringify(messageData.location));
    }

    if (messageData.replyTo) {
        formData.append('replyTo', messageData.replyTo);
    }

    const { data } = await chatApi.post(`/rooms/${roomId}/messages`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

export const markMessageAsRead = async (roomId, messageId) => {
    const { data } = await chatApi.post(
        `/rooms/${roomId}/messages/${messageId}/read`
    );
    return data;
};

export const markAllMessagesAsRead = async (roomId) => {
    const { data } = await chatApi.post(`/rooms/${roomId}/messages/read-all`);
    return data;
};

export const deleteMessage = async (roomId, messageId) => {
    const { data } = await chatApi.delete(
        `/rooms/${roomId}/messages/${messageId}`
    );
    return data;
};

export const editMessage = async (roomId, messageId, content) => {
    const { data } = await chatApi.put(
        `/rooms/${roomId}/messages/${messageId}`,
        {
            content,
        }
    );
    return data;
};

// File sharing
export const uploadChatFile = async (roomId, file, onProgress) => {
    const formData = new FormData();
    formData.append('file', file);

    const { data } = await chatApi.post(`/rooms/${roomId}/upload`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
            if (onProgress) {
                const percentCompleted = Math.round(
                    (progressEvent.loaded * 100) / progressEvent.total
                );
                onProgress(percentCompleted);
            }
        },
    });
    return data;
};

export const downloadChatFile = async (fileId) => {
    const { data } = await chatApi.get(`/files/${fileId}/download`, {
        responseType: 'blob',
    });
    return data;
};

// Land discussion specific
export const createLandDiscussion = async (landId, participants) => {
    const { data } = await chatApi.post('/land-discussion', {
        landId,
        participants,
    });
    return data;
};

export const getLandDiscussions = async (landId) => {
    const { data } = await chatApi.get(`/land-discussion/land/${landId}`);
    return data;
};

// Contractor group chats
export const createContractorGroup = async (
    contractorId,
    participants,
    projectDetails
) => {
    const { data } = await chatApi.post('/contractor-group', {
        contractorId,
        participants,
        projectDetails,
    });
    return data;
};

export const getContractorGroups = async (contractorId) => {
    const { data } = await chatApi.get(
        `/contractor-group/contractor/${contractorId}`
    );
    return data;
};

// Broker chats
export const createBrokerChat = async (brokerId, clientId, landId = null) => {
    const { data } = await chatApi.post('/broker-chat', {
        brokerId,
        clientId,
        landId,
    });
    return data;
};

export const getBrokerChats = async (brokerId) => {
    const { data } = await chatApi.get(`/broker-chat/broker/${brokerId}`);
    return data;
};

// Search and filters
export const searchMessages = async (roomId, query) => {
    const { data } = await chatApi.get(`/rooms/${roomId}/search`, {
        params: { q: query },
    });
    return data;
};

export const getChatMedia = async (roomId, type = 'all') => {
    const { data } = await chatApi.get(`/rooms/${roomId}/media`, {
        params: { type }, // 'images', 'files', 'all'
    });
    return data;
};

// Typing indicators
export const sendTypingIndicator = (roomId, isTyping) => {
    if (socket) {
        socket.emit('typing', { roomId, isTyping });
    }
};

// Online status
export const updateOnlineStatus = (status) => {
    if (socket) {
        socket.emit('status', { status }); // 'online', 'away', 'offline'
    }
};

// Socket event handlers
export const setupSocketListeners = (callbacks) => {
    if (!socket) return;

    // Message events
    socket.on('message', callbacks.onMessage);
    socket.on('messageRead', callbacks.onMessageRead);
    socket.on('messageDeleted', callbacks.onMessageDeleted);
    socket.on('messageEdited', callbacks.onMessageEdited);

    // Room events
    socket.on('roomCreated', callbacks.onRoomCreated);
    socket.on('userJoined', callbacks.onUserJoined);
    socket.on('userLeft', callbacks.onUserLeft);

    // Typing events
    socket.on('userTyping', callbacks.onUserTyping);
    socket.on('userStoppedTyping', callbacks.onUserStoppedTyping);

    // Status events
    socket.on('userOnline', callbacks.onUserOnline);
    socket.on('userOffline', callbacks.onUserOffline);

    // Error events
    socket.on('error', callbacks.onError);
    socket.on('connect_error', callbacks.onConnectError);
};

export const removeSocketListeners = () => {
    if (!socket) return;

    socket.off('message');
    socket.off('messageRead');
    socket.off('messageDeleted');
    socket.off('messageEdited');
    socket.off('roomCreated');
    socket.off('userJoined');
    socket.off('userLeft');
    socket.off('userTyping');
    socket.off('userStoppedTyping');
    socket.off('userOnline');
    socket.off('userOffline');
    socket.off('error');
    socket.off('connect_error');
};

// Notification settings
export const updateNotificationSettings = async (roomId, settings) => {
    const { data } = await chatApi.put(
        `/rooms/${roomId}/notifications`,
        settings
    );
    return data;
};

export const getNotificationSettings = async (roomId) => {
    const { data } = await chatApi.get(`/rooms/${roomId}/notifications`);
    return data;
};

// Chat analytics
export const getChatAnalytics = async (roomId, timeframe = '7d') => {
    const { data } = await chatApi.get(`/rooms/${roomId}/analytics`, {
        params: { timeframe },
    });
    return data;
};

export default chatApi;
