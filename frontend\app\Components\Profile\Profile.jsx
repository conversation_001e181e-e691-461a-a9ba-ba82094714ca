import React, { useContext } from 'react';
import {
    SafeAreaView,
    ScrollView,
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    Pressable,
} from 'react-native';
import Ionicons from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';

const PROFILE_OPTIONS = [
    {
        key: 'communication',
        icon: 'notifications-outline',
        label: 'Communication Settings',
        url: '',
    },
    {
        key: 'favourites',
        icon: 'heart-outline',
        label: 'Your Favourites',
        url: '',
    },
    {
        key: 'listings',
        icon: 'list-outline',
        label: 'Manage Listings',
        url: '',
    },
    {
        key: 'payments',
        icon: 'card-outline',
        label: 'Manage Payments',
        url: '',
    },
    {
        key: 'logout',
        icon: 'log-out-outline',
        label: 'Logout',
        url: '../auth/Login',
    },
];

export default function ProfileSection() {
    const { theme } = useContext(ThemeContext);
    const user = {
        avatarUri: null, // replace with real URI or require(...)
        name: '<PERSON><PERSON>',
    };

    const router = useRouter();
    return (
        <SafeAreaView style={styles.safe}>
            <ScrollView contentContainerStyle={styles.container}>
                {/* Avatar + Name */}
                <View style={styles.header}>
                    <View style={styles.avatarContainer}>
                        {user.avatarUri ? (
                            <Image
                                source={{ uri: user.avatarUri }}
                                style={styles.avatar}
                            />
                        ) : (
                            <Ionicons
                                name="person-circle-outline"
                                size={80}
                                color="gray"
                            />
                        )}
                    </View>
                    <Text style={styles.name}>{user.name}</Text>
                    <Pressable
                        onPress={() =>
                            router.push('/Components/Profile/ProfileSetting')
                        }
                    >
                        <Text
                            style={{
                                color: theme.PRIMARY,
                                marginTop: 5,
                            }}
                        >
                            Edit Profile
                        </Text>
                    </Pressable>
                </View>

                {/* Option List */}
                <View style={styles.card}>
                    {PROFILE_OPTIONS.map(({ key, icon, label, url }) => (
                        <TouchableOpacity
                            key={key}
                            style={styles.optionRow}
                            onPress={() => router.push(url)}
                        >
                            <View style={styles.optionLeft}>
                                <Ionicons
                                    name={icon}
                                    size={22}
                                    color={theme.PRIMARY}
                                />
                                <Text style={styles.optionLabel}>{label}</Text>
                            </View>
                            <Ionicons
                                name="chevron-forward-outline"
                                size={20}
                                color="gray"
                            />
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safe: {
        flex: 1,
        backgroundColor: '#f2f2f2',
    },
    container: {
        paddingVertical: 30,
        alignItems: 'center',
    },
    header: {
        alignItems: 'center',
        marginBottom: 25,
        width: '90%',
        backgroundColor: '#fff',
        paddingVertical: 20,
        borderRadius: 10,
        elevation: 2,
    },
    avatarContainer: {
        backgroundColor: '#e1e1e1',
        borderRadius: 50,
        width: 100,
        height: 100,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        marginBottom: 12,
    },
    avatar: {
        width: '100%',
        height: '100%',
    },
    name: {
        fontSize: 20,
        fontWeight: '600',
        color: '#333',
    },
    subtitle: {
        fontSize: 14,
        color: 'gray',
        marginTop: 4,
    },
    card: {
        width: '90%',
        backgroundColor: '#fff',
        borderRadius: 10,
        elevation: 1,
        overflow: 'hidden',
    },
    optionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 15,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#ddd',
    },
    optionLeft: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    optionLabel: {
        marginLeft: 15,
        fontSize: 16,
        color: '#333',
    },
});
