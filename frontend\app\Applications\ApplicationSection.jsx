import React from 'react';
import { View, Text } from 'react-native';
import ApplicationCard from './ApplicationCard';
import styles from './styles';

const ApplicationSection = ({
    title,
    applications,
    theme,
    handleViewDetails,
    handleEdit,
    handleDelete,
    isRequests,
}) => {
    return (
        <View style={[styles.cardContainer, { backgroundColor: theme.CARD }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                {title}
            </Text>
            {applications.length > 0 ? (
                applications.map((item, index) => (
                    <ApplicationCard
                        key={item.id}
                        item={item}
                        index={index}
                        theme={theme}
                        handleViewDetails={handleViewDetails}
                        handleEdit={handleEdit}
                        handleDelete={handleDelete}
                        isRequests={isRequests}
                    />
                ))
            ) : (
                <Text
                    style={[styles.noDataText, { color: theme.TEXT_SECONDARY }]}
                >
                    No {title.toLowerCase()} available.
                </Text>
            )}
        </View>
    );
};

export default ApplicationSection;
