import { Text, TouchableOpacity } from 'react-native';
import React, { useContext } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';

export default function SupportQuickAccess({ onPress }) {
    const { theme } = useContext(ThemeContext);
    return (
        <TouchableOpacity
            style={{
                backgroundColor: theme.PRIMARY,
                borderRadius: 16,
                padding: 18,
                flexDirection: 'row',
                alignItems: 'center',
                margin: 16,
                shadowColor: theme.PRIMARY,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.15,
                shadowRadius: 8,
                elevation: 4,
            }}
            onPress={onPress}
            activeOpacity={0.85}
        >
            <Ionicons
                name="help-circle-outline"
                size={28}
                color={theme.WHITE}
                style={{ marginRight: 12 }}
            />
            <Text
                style={{ color: theme.WHITE, fontWeight: 'bold', fontSize: 16 }}
            >
                Need Support? Tap here!
            </Text>
        </TouchableOpacity>
    );
}
