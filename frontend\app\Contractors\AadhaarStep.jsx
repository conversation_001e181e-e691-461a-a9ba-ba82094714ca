import React, { useState } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import ModalDatePicker from 'react-native-modal-datetime-picker';
import { Picker } from '@react-native-picker/picker';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from './styles';
import { showToast } from '../../utils/showToast';
import {
    normalizeAadhaar,
    getFileNameFromUrl,
} from '../../utils/BrokerFormUtils';
import ScanningAnimation from '../Components/ScanningAnimation';

const AadhaarStep = ({ formik, theme, setStep, handlePreviewDocument }) => {
    const [showAadhaarDatePicker, setShowAadhaarDatePicker] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [isValidated, setIsValidated] = useState(false);
    const [showScanning, setShowScanning] = useState(false);

    const pickDocument = async () => {
        setIsUploading(true);
        try {
            const result = await DocumentPicker.getDocumentAsync({
                type: ['image/jpeg', 'image/png', 'application/pdf'],
            });
            if (!result.canceled && result.assets?.[0]) {
                const file = result.assets[0];
                const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
                const validExtensions = ['.jpg', '.jpeg', '.png', '.pdf'];
                if (file.size > MAX_FILE_SIZE) {
                    showToast(
                        'error',
                        'File Size Error',
                        'File size exceeds the limit of 5MB.'
                    );
                    return;
                }
                if (
                    !validExtensions.some((ext) =>
                        file.name.toLowerCase().endsWith(ext)
                    )
                ) {
                    showToast(
                        'error',
                        'File Type Error',
                        'Only JPEG, PNG, and PDF are allowed.'
                    );
                    return;
                }
                formik.setFieldValue('aadhaarDocument', file);
            }
        } catch (error) {
            showToast(
                'error',
                'Document Picker Error',
                'Failed to select file'
            );
        } finally {
            setIsUploading(false);
        }
    };

    const handleValidate = async () => {
        // Check if all required fields are filled and valid
        const errors = await formik.validateForm();
        const fields = [
            'aadhaarNumber',
            'nameOnAadhaar',
            'dateOfBirth',
            'gender',
            'address',
            'aadhaarDocument',
        ];

        const hasErrors = fields.some((field) => errors[field]);

        if (hasErrors) {
            fields.forEach((field) => formik.setFieldTouched(field, true));
            showToast(
                'error',
                'Validation Error',
                'Please fill all required fields correctly before validating.'
            );
            return;
        }

        // Start scanning animation
        setShowScanning(true);
    };

    const handleScanningComplete = () => {
        setShowScanning(false);
        setIsValidated(true);
        showToast(
            'success',
            'Validation Successful',
            'Aadhaar details have been validated successfully!'
        );
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Aadhaar Details
            </Text>
            {formik.touched.aadhaarNumber && formik.errors.aadhaarNumber && (
                <Text style={styles.errorText}>
                    {formik.errors.aadhaarNumber}
                </Text>
            )}

            {/* Name on Aadhaar */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.nameOnAadhaar && formik.errors.nameOnAadhaar
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="person-outline"
                    size={22}
                    color={
                        formik.touched.nameOnAadhaar &&
                            formik.errors.nameOnAadhaar
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Name on Aadhaar"
                    value={formik.values.nameOnAadhaar}
                    onChangeText={formik.handleChange('nameOnAadhaar')}
                    onBlur={formik.handleBlur('nameOnAadhaar')}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    accessibilityLabel="Enter name on Aadhaar"
                    testID="nameOnAadhaar-input"
                />
                {formik.values.nameOnAadhaar.length > 0 && (
                    <TouchableOpacity
                        onPress={() =>
                            formik.setFieldValue('nameOnAadhaar', '')
                        }
                        style={styles.clearButton}
                    >
                        <Ionicons name="close-circle" size={20} color="#999" />
                    </TouchableOpacity>
                )}
            </View>
            {formik.touched.nameOnAadhaar && formik.errors.nameOnAadhaar && (
                <Text style={styles.errorText}>
                    {formik.errors.nameOnAadhaar}
                </Text>
            )}

            {/* Aadhaar Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.aadhaarNumber && formik.errors.aadhaarNumber
                        ? styles.inputError
                        : null,
                ]}
            >
                <MaterialCommunityIcons
                    name="card-account-details-outline"
                    size={22}
                    color={
                        formik.touched.aadhaarNumber &&
                            formik.errors.aadhaarNumber
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Aadhaar Number"
                    value={formik.values.aadhaarNumber}
                    onChangeText={(text) =>
                        formik.setFieldValue(
                            'aadhaarNumber',
                            normalizeAadhaar(text)
                        )
                    }
                    onBlur={formik.handleBlur('aadhaarNumber')}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    keyboardType="numeric"
                    maxLength={12}
                    accessibilityLabel="Enter Aadhaar number"
                    testID="aadhaarNumber-input"
                />
                {formik.values.aadhaarNumber.length > 0 && (
                    <TouchableOpacity
                        onPress={() =>
                            formik.setFieldValue('aadhaarNumber', '')
                        }
                        style={styles.clearButton}
                    >
                        <Ionicons name="close-circle" size={20} color="#999" />
                    </TouchableOpacity>
                )}
            </View>

            {/* Date of Birth */}
            <Pressable
                onPress={() => setShowAadhaarDatePicker(true)}
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.dateOfBirth && formik.errors.dateOfBirth
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="calendar-outline"
                    size={22}
                    color={
                        formik.touched.dateOfBirth && formik.errors.dateOfBirth
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.dateText,
                        {
                            color: formik.values.dateOfBirth
                                ? theme.TEXT_PRIMARY
                                : theme.TEXT_PLACEHOLDER,
                        },
                    ]}
                >
                    {formik.values.dateOfBirth
                        ? new Date(
                            formik.values.dateOfBirth
                        ).toLocaleDateString()
                        : 'Date of Birth'}
                </Text>
            </Pressable>
            <ModalDatePicker
                isVisible={showAadhaarDatePicker}
                mode="date"
                date={formik.values.dateOfBirth || new Date()}
                onConfirm={(date) => {
                    formik.setFieldValue('dateOfBirth', date);
                    setShowAadhaarDatePicker(false);
                }}
                onCancel={() => setShowAadhaarDatePicker(false)}
                maximumDate={new Date()}
                testID="aadhaarDatePicker"
            />
            {formik.touched.dateOfBirth && formik.errors.dateOfBirth && (
                <Text style={styles.errorText}>
                    {formik.errors.dateOfBirth}
                </Text>
            )}

            {/* Gender */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.gender && formik.errors.gender
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="people-outline"
                    size={22}
                    color={
                        formik.touched.gender && formik.errors.gender
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}
                >
                    <Picker
                        selectedValue={formik.values.gender}
                        onValueChange={(value) =>
                            formik.setFieldValue('gender', value)
                        }
                        onBlur={() => formik.setFieldTouched('gender', true)}
                        style={[
                            styles.picker,
                            {
                                color: formik.values.gender
                                    ? theme.TEXT_PRIMARY
                                    : theme.TEXT_PLACEHOLDER,
                                marginLeft: -15,
                                flex: 1,
                                height: 56,
                                backgroundColor: 'transparent',
                            },
                        ]}
                        itemStyle={{
                            fontSize: 16,
                            color: theme.TEXT_PRIMARY,
                            paddingLeft: 0,
                            marginLeft: 0,
                            height: 56,
                            textAlignVertical: 'center',
                        }}
                        dropdownIconColor={
                            formik.touched.gender && formik.errors.gender
                                ? 'red'
                                : theme.PRIMARY
                        }
                        dropdownIconRippleColor={theme.PRIMARY}
                        dropdownIconSize={22}
                        accessibilityLabel="Select gender"
                        testID="gender-picker"
                    >
                        <Picker.Item label="Select Gender" value="" />
                        <Picker.Item label="Male" value="Male" />
                        <Picker.Item label="Female" value="Female" />
                        <Picker.Item label="Other" value="Other" />
                    </Picker>
                </View>
            </View>
            {formik.touched.gender && formik.errors.gender && (
                <Text style={styles.errorText}>{formik.errors.gender}</Text>
            )}

            {/* Address */}
            <View
                style={[
                    styles.inputContainer,
                    styles.addressInputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                    formik.touched.address && formik.errors.address
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="home-outline"
                    size={22}
                    color={
                        formik.touched.address && formik.errors.address
                            ? 'red'
                            : theme.PRIMARY
                    }
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address"
                    value={formik.values.address}
                    onChangeText={formik.handleChange('address')}
                    onBlur={formik.handleBlur('address')}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    multiline
                    accessibilityLabel="Enter address"
                    testID="address-input"
                />
                {formik.values.address.length > 0 && (
                    <TouchableOpacity
                        onPress={() => formik.setFieldValue('address', '')}
                        style={styles.clearButton}
                    >
                        <Ionicons name="close-circle" size={20} color="#999" />
                    </TouchableOpacity>
                )}
            </View>
            {formik.touched.address && formik.errors.address && (
                <Text style={styles.errorText}>{formik.errors.address}</Text>
            )}
            <TouchableOpacity
                onPress={pickDocument}
                disabled={isUploading}
                style={[
                    styles.fileButton,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                        opacity: isUploading ? 0.6 : 1,
                    },
                    formik.touched.aadhaarDocument &&
                        formik.errors.aadhaarDocument
                        ? styles.inputError
                        : null,
                ]}
            >
                <Ionicons
                    name="cloud-upload-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.fileButtonText,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                >
                    {formik.values.aadhaarDocument
                        ? typeof formik.values.aadhaarDocument === 'string'
                            ? getFileNameFromUrl(formik.values.aadhaarDocument)
                            : formik.values.aadhaarDocument.name
                        : 'Upload Aadhaar Document'}
                </Text>
                {formik.values.aadhaarDocument && (
                    <TouchableOpacity
                        onPress={() =>
                            handlePreviewDocument(formik.values.aadhaarDocument)
                        }
                        style={styles.previewButton}
                    >
                        <Ionicons
                            name="eye-outline"
                            size={22}
                            color={theme.GRAY}
                        />
                    </TouchableOpacity>
                )}
            </TouchableOpacity>
            {formik.touched.aadhaarDocument &&
                formik.errors.aadhaarDocument && (
                    <Text style={styles.errorText}>
                        {formik.errors.aadhaarDocument}
                    </Text>
                )}

            {/* Validate Button */}
            <TouchableOpacity
                style={[
                    styles.validateButton,
                    { borderColor: theme.PRIMARY },
                    isValidated && styles.disabledButton,
                ]}
                onPress={handleValidate}
                disabled={isValidated}
                accessibilityLabel="Validate Aadhaar details"
                accessibilityRole="button"
            >
                <LinearGradient
                    colors={isValidated ? [theme.SUCCESS, theme.SUCCESS] : [theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.validateButtonGradient}
                >
                    <Text
                        style={[
                            styles.validateButtonText,
                            { color: theme.WHITE },
                        ]}
                    >
                        {isValidated ? '✓ Validated' : 'Validate'}
                    </Text>
                </LinearGradient>
            </TouchableOpacity>

            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        { flex: 1, marginRight: 8, borderColor: theme.PRIMARY },
                    ]}
                    onPress={() => setStep('userDetails')}
                    accessibilityLabel="Go back to user details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.WHITE, theme.GRAY_LIGHT]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Back
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        { flex: 1, marginLeft: 8, borderColor: theme.ACCENT },
                        !isValidated && styles.disabledButton,
                    ]}
                    onPress={async () => {
                        if (!isValidated) {
                            showToast(
                                'warning',
                                'Validation Required',
                                'Please validate your Aadhaar details first.'
                            );
                            return;
                        }
                        setStep('pan');
                    }}
                    disabled={!isValidated}
                    accessibilityLabel="Proceed to PAN details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>

            {/* Scanning Animation */}
            <ScanningAnimation
                visible={showScanning}
                onComplete={handleScanningComplete}
                theme={theme}
                styles={styles}
                documentType="Aadhaar"
            />
        </>
    );
};

export default AadhaarStep;
