import React, { useContext, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    RefreshControl,
    Image,
    Dimensions,
    Animated,
    Easing,
    ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchUserProjects } from '../../api/projects/projectApi';
import Toast from 'react-native-toast-message';

const { width, height } = Dimensions.get('window');

export default function ProjectsIndex() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();

    // Animation refs
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    const {
        data: projectsData,
        isLoading,
        error,
        refetch,
        isRefetching,
    } = useQuery({
        queryKey: ['userProjects'],
        queryFn: fetchUserProjects,
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to fetch projects',
            });
        },
    });

    const projects = projectsData?.projects || [];

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Budget not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Budget not specified';
    };

    const ProjectCard = ({ project }) => (
        <TouchableOpacity
            style={[styles.projectCard, { backgroundColor: theme.CARD }]}
            onPress={() =>
                router.push(`/Projects/ProjectDetails?id=${project._id}`)
            }
            activeOpacity={0.8}
        >
            <View style={styles.cardHeader}>
                <View style={styles.projectInfo}>
                    <Text
                        style={[
                            styles.projectName,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                        numberOfLines={1}
                    >
                        {project.projectName}
                    </Text>
                    <Text
                        style={[
                            styles.projectType,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {project.projectType || 'Not specified'} • {project.constructionType || 'Not specified'}
                    </Text>
                </View>
                <View style={styles.badgeContainer}>
                    {/* Role Badge */}
                    {project.userRoleInProject && project.userRoleInProject !== 'owner' && (
                        <View
                            style={[
                                styles.roleBadge,
                                {
                                    backgroundColor: project.userRoleInProject === 'contractor'
                                        ? theme.SUCCESS + '20'
                                        : theme.WARNING + '20'
                                },
                            ]}
                        >
                            <Text style={[
                                styles.roleText,
                                {
                                    color: project.userRoleInProject === 'contractor'
                                        ? theme.SUCCESS
                                        : theme.WARNING
                                }
                            ]}>
                                {project.userRoleInProject === 'contractor' ? 'Hired as Contractor' : 'Hired as Broker'}
                            </Text>
                        </View>
                    )}
                    <View
                        style={[
                            styles.statusBadge,
                            { backgroundColor: theme.PRIMARY + '20' },
                        ]}
                    >
                        <Text style={[styles.statusText, { color: theme.PRIMARY }]}>
                            Active
                        </Text>
                    </View>
                </View>
            </View>

            <View style={styles.cardContent}>
                <View style={styles.detailRow}>
                    <MaterialIcons
                        name="location-on"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.detailText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                        numberOfLines={1}
                    >
                        {project.location?.city}, {project.location?.state}
                    </Text>
                </View>

                <View style={styles.detailRow}>
                    <MaterialIcons
                        name="attach-money"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.detailText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {formatBudget(project.budget)}
                    </Text>
                </View>

                {project.designPreferences && (
                    <View style={styles.detailRow}>
                        <MaterialIcons
                            name="home"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {project.designPreferences.floors &&
                                `${project.designPreferences.floors} Floors`}
                            {project.designPreferences.bedrooms &&
                                ` • ${project.designPreferences.bedrooms} BHK`}
                        </Text>
                    </View>
                )}
            </View>

            <View style={styles.cardFooter}>
                <View style={styles.teamInfo}>
                    {project.brokerId && (
                        <View style={styles.teamMember}>
                            <MaterialIcons
                                name="business"
                                size={14}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.teamText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                Broker Hired
                            </Text>
                        </View>
                    )}
                    {project.contractorId && (
                        <View style={styles.teamMember}>
                            <MaterialIcons
                                name="build"
                                size={14}
                                color={theme.SUCCESS}
                            />
                            <Text
                                style={[
                                    styles.teamText,
                                    { color: theme.SUCCESS },
                                ]}
                            >
                                Contractor Hired
                            </Text>
                        </View>
                    )}
                    {!project.brokerId && !project.contractorId && (
                        <Text
                            style={[
                                styles.noTeamText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            No professionals hired yet
                        </Text>
                    )}
                </View>
                <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={theme.TEXT_SECONDARY}
                />
            </View>
        </TouchableOpacity>
    );

    const EmptyState = () => (
        <View style={styles.emptyContainer}>
            <MaterialIcons
                name="assignment"
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No Projects Yet
            </Text>
            <Text
                style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}
            >
                Create your first project to get started with finding the right
                professionals
            </Text>
            <TouchableOpacity
                style={styles.createButton}
                onPress={() => router.push('/Projects/CreateProject')}
                activeOpacity={0.8}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.gradient}
                />
                <Text style={[styles.createButtonText, { color: theme.WHITE }]}>
                    Create Project
                </Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header with Gradient */}
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    style={styles.header}
                >
                    <View style={styles.headerContent}>
                        <TouchableOpacity onPress={() => router.back()}>
                            <Ionicons name="arrow-back" size={24} color="#fff" />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle}>My Projects</Text>
                        <TouchableOpacity
                            onPress={() => router.push('/Projects/CreateProject')}
                            activeOpacity={0.8}
                        >
                            <Ionicons name="add" size={24} color="#fff" />
                        </TouchableOpacity>
                    </View>
                </LinearGradient>

                <ScrollView
                    style={styles.scrollView}
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl
                            refreshing={isRefetching}
                            onRefresh={refetch}
                            colors={[theme.PRIMARY]}
                            tintColor={theme.PRIMARY}
                        />
                    }
                >
                    <Animated.View
                        style={[
                            styles.content,
                            {
                                transform: [{ scale: scaleAnim }],
                                opacity: fadeAnim,
                            },
                        ]}
                    >
                        {isLoading ? (
                            <View style={styles.loadingContainer}>
                                <Text
                                    style={[
                                        styles.loadingText,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Loading projects...
                                </Text>
                            </View>
                        ) : projects.length === 0 ? (
                            <EmptyState />
                        ) : (
                            <View style={styles.projectsList}>
                                {projects.map((project) => (
                                    <ProjectCard
                                        key={project._id}
                                        project={project}
                                    />
                                ))}
                            </View>
                        )}
                    </Animated.View>
                </ScrollView>
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    safeArea: {
        flex: 1,
    },
    header: {
        paddingTop: 20,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingTop: 20,
        paddingBottom: 100,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    loadingText: {
        fontSize: 16,
    },
    projectsList: {
        gap: 16,
    },
    projectCard: {
        borderRadius: 20,
        padding: 20,
        marginHorizontal: 20,
        marginBottom: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    projectInfo: {
        flex: 1,
        marginRight: 12,
    },
    projectName: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 4,
    },
    projectType: {
        fontSize: 14,
    },
    badgeContainer: {
        alignItems: 'flex-end',
        gap: 4,
    },
    roleBadge: {
        paddingHorizontal: 8,
        paddingVertical: 3,
        borderRadius: 10,
    },
    roleText: {
        fontSize: 10,
        fontWeight: '600',
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    cardContent: {
        marginBottom: 12,
        gap: 8,
    },
    detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    detailText: {
        fontSize: 14,
        flex: 1,
    },
    cardFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    teamInfo: {
        flex: 1,
        flexDirection: 'row',
        gap: 12,
    },
    teamMember: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    teamText: {
        fontSize: 12,
        fontWeight: '500',
    },
    noTeamText: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    emptyTitle: {
        fontSize: 24,
        fontWeight: '700',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 32,
    },
    createButton: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 4,
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    createButtonText: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
        paddingVertical: 16,
        paddingHorizontal: 32,
    },
});
