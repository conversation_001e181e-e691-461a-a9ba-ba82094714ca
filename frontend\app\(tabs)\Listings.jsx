import { useContext, useState } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    RefreshControl,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchUserProjects } from '../../api/projects/projectApi';
import Header from '../Components/Home/Header';

export default function Listings() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState('projects'); // 'projects' or 'sites'

    const {
        data: projectsData,
        isLoading: projectsLoading,
        error: projectsError,
        refetch: refetchProjects,
        isRefetching: projectsRefetching,
    } = useQuery({
        queryKey: ['userProjects'],
        queryFn: fetchUserProjects,
    });

    const projects = projectsData?.projects || [];

    const handleRefresh = async () => {
        setRefreshing(true);
        await refetchProjects();
        setRefreshing(false);
    };

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Budget not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Budget not specified';
    };

    const ProjectCard = ({ project }) => (
        <TouchableOpacity
            style={[styles.projectCard, { backgroundColor: theme.CARD }]}
            onPress={() =>
                router.push(`/Projects/ProjectDetails?id=${project._id}`)
            }
            activeOpacity={0.8}
        >
            <View style={styles.cardHeader}>
                <View style={styles.projectInfo}>
                    <Text
                        style={[
                            styles.projectName,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                        numberOfLines={1}
                    >
                        {project.projectName}
                    </Text>
                    <Text
                        style={[
                            styles.projectType,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {project.projectType || 'Not specified'} • {project.constructionType || 'Not specified'}
                    </Text>
                </View>
                <View
                    style={[
                        styles.statusBadge,
                        { backgroundColor: theme.PRIMARY + '20' },
                    ]}
                >
                    <Text style={[styles.statusText, { color: theme.PRIMARY }]}>
                        Active
                    </Text>
                </View>
            </View>

            <View style={styles.cardContent}>
                <View style={styles.detailRow}>
                    <MaterialIcons
                        name="location-on"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.detailText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                        numberOfLines={1}
                    >
                        {project.location?.city || 'Not specified'}, {project.location?.state || 'Not specified'}
                    </Text>
                </View>

                <View style={styles.detailRow}>
                    <MaterialIcons
                        name="attach-money"
                        size={16}
                        color={theme.TEXT_SECONDARY}
                    />
                    <Text
                        style={[
                            styles.detailText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {formatBudget(project.budget)}
                    </Text>
                </View>

                {project.designPreferences && (
                    <View style={styles.detailRow}>
                        <MaterialIcons
                            name="home"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {project.designPreferences?.floors &&
                                `${project.designPreferences.floors} Floors`}
                            {project.designPreferences?.bedrooms &&
                                ` • ${project.designPreferences.bedrooms} BHK`}
                            {!project.designPreferences?.floors && !project.designPreferences?.bedrooms &&
                                'Design preferences not specified'}
                        </Text>
                    </View>
                )}
            </View>

            <View style={styles.cardFooter}>
                <View style={styles.teamInfo}>
                    {project.brokerId && (
                        <View style={styles.teamMember}>
                            <MaterialIcons
                                name="business"
                                size={14}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.teamText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                Broker Hired
                            </Text>
                        </View>
                    )}
                    {project.contractorId && (
                        <View style={styles.teamMember}>
                            <MaterialIcons
                                name="build"
                                size={14}
                                color={theme.SUCCESS}
                            />
                            <Text
                                style={[
                                    styles.teamText,
                                    { color: theme.SUCCESS },
                                ]}
                            >
                                Contractor Hired
                            </Text>
                        </View>
                    )}
                    {!project.brokerId && !project.contractorId && (
                        <Text
                            style={[
                                styles.noTeamText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            No professionals hired yet
                        </Text>
                    )}
                </View>
                <View style={styles.actionButtons}>
                    {project.brokerAssistanceRequired && !project.brokerId && (
                        <TouchableOpacity
                            style={[styles.miniButton, { backgroundColor: theme.PRIMARY + '20' }]}
                            onPress={() => router.push(`/Projects/HireProfessionals?projectId=${project._id}&type=broker`)}
                            activeOpacity={0.8}
                        >
                            <MaterialIcons
                                name="business"
                                size={16}
                                color={theme.PRIMARY}
                            />
                        </TouchableOpacity>
                    )}
                    {!project.contractorId && (!project.brokerAssistanceRequired || project.brokerId) && (
                        <TouchableOpacity
                            style={[styles.miniButton, { backgroundColor: theme.SUCCESS + '20' }]}
                            onPress={() => router.push(`/Projects/HireProfessionals?projectId=${project._id}&type=contractor`)}
                            activeOpacity={0.8}
                        >
                            <MaterialIcons
                                name="build"
                                size={16}
                                color={theme.SUCCESS}
                            />
                        </TouchableOpacity>
                    )}
                    <TouchableOpacity
                        style={[styles.miniButton, { backgroundColor: theme.TEXT_SECONDARY + '20' }]}
                        onPress={() => router.push(`/Projects/ProjectDetails?id=${project._id}`)}
                        activeOpacity={0.8}
                    >
                        <Ionicons
                            name="chevron-forward"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </TouchableOpacity>
    );

    const EmptyState = () => (
        <View style={styles.emptyContainer}>
            <MaterialIcons
                name="assignment"
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No Projects Yet
            </Text>
            <Text
                style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}
            >
                Create your first project to get started with finding the right
                professionals for your construction needs
            </Text>
            <TouchableOpacity
                style={styles.createButton}
                onPress={() => router.push('/Projects/CreateProject')}
                activeOpacity={0.8}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.gradient}
                />
                <Text style={[styles.createButtonText, { color: theme.WHITE }]}>
                    Create Project
                </Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Header */}
            <View style={styles.fixedTop}>
                <Header />
                <View style={[styles.subHeader, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}
                    >
                        My Projects & Sites
                    </Text>
                </View>

                {/* Tab Navigation */}
                <View style={[styles.tabContainer, { backgroundColor: theme.CARD }]}>
                    <TouchableOpacity
                        style={[
                            styles.tab,
                            activeTab === 'projects' && { backgroundColor: theme.PRIMARY + '20' }
                        ]}
                        onPress={() => setActiveTab('projects')}
                        activeOpacity={0.8}
                    >
                        <MaterialIcons
                            name="assignment"
                            size={20}
                            color={activeTab === 'projects' ? theme.PRIMARY : theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.tabText,
                                {
                                    color: activeTab === 'projects' ? theme.PRIMARY : theme.TEXT_SECONDARY,
                                    fontWeight: activeTab === 'projects' ? '600' : '400'
                                }
                            ]}
                        >
                            Projects
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.tab,
                            activeTab === 'sites' && { backgroundColor: theme.PRIMARY + '20' }
                        ]}
                        onPress={() => setActiveTab('sites')}
                        activeOpacity={0.8}
                    >
                        <MaterialIcons
                            name="location-on"
                            size={20}
                            color={activeTab === 'sites' ? theme.PRIMARY : theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.tabText,
                                {
                                    color: activeTab === 'sites' ? theme.PRIMARY : theme.TEXT_SECONDARY,
                                    fontWeight: activeTab === 'sites' ? '600' : '400'
                                }
                            ]}
                        >
                            Saved Sites
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    activeTab === 'projects' ? (
                        <RefreshControl
                            refreshing={refreshing || projectsRefetching}
                            onRefresh={handleRefresh}
                            colors={[theme.PRIMARY]}
                            tintColor={theme.PRIMARY}
                        />
                    ) : undefined
                }
            >
                <View style={styles.content}>
                    {activeTab === 'projects' ? (
                        projectsLoading ? (
                            <View style={styles.loadingContainer}>
                                <Text
                                    style={[
                                        styles.loadingText,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Loading projects...
                                </Text>
                            </View>
                        ) : projectsError ? (
                            <View style={styles.errorContainer}>
                                <MaterialIcons
                                    name="error-outline"
                                    size={48}
                                    color={theme.ERROR}
                                />
                                <Text
                                    style={[
                                        styles.errorTitle,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Failed to load projects
                                </Text>
                                <Text
                                    style={[
                                        styles.errorSubtitle,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    {projectsError.message || 'Please try again later'}
                                </Text>
                                <TouchableOpacity
                                    style={[
                                        styles.retryButton,
                                        { backgroundColor: theme.PRIMARY },
                                    ]}
                                    onPress={refetchProjects}
                                >
                                    <Text
                                        style={[
                                            styles.retryButtonText,
                                            { color: theme.BACKGROUND },
                                        ]}
                                    >
                                        Retry
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        ) : projects.length === 0 ? (
                            <EmptyState />
                        ) : (
                            <View style={styles.projectsList}>
                                {projects.map((project) => (
                                    <ProjectCard
                                        key={project._id}
                                        project={project}
                                    />
                                ))}
                            </View>
                        )
                    ) : (
                        <View style={styles.comingSoonContainer}>
                            <MaterialIcons
                                name="location-on"
                                size={80}
                                color={theme.TEXT_SECONDARY}
                            />
                            <Text style={[styles.comingSoonTitle, { color: theme.TEXT_PRIMARY }]}>
                                Saved Sites
                            </Text>
                            <Text
                                style={[styles.comingSoonSubtitle, { color: theme.TEXT_SECONDARY }]}
                            >
                                Your saved plots and sites will appear here.
                            </Text>
                        </View>
                    )}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    fixedTop: {
        paddingBottom: 8,
    },
    subHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        elevation: 2,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: '700',
    },
    addButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    scrollView: {
        flex: 1,
    },
    content: {
        padding: 16,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    loadingText: {
        fontSize: 16,
    },
    projectsList: {
        gap: 16,
    },
    projectCard: {
        borderRadius: 12,
        padding: 16,
        elevation: 2,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    projectInfo: {
        flex: 1,
        marginRight: 12,
    },
    projectName: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 4,
    },
    projectType: {
        fontSize: 14,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    cardContent: {
        marginBottom: 12,
        gap: 8,
    },
    detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    detailText: {
        fontSize: 14,
        flex: 1,
    },
    cardFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    teamInfo: {
        flex: 1,
        flexDirection: 'row',
        gap: 12,
    },
    teamMember: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    teamText: {
        fontSize: 12,
        fontWeight: '500',
    },
    noTeamText: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 8,
    },
    miniButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    emptyTitle: {
        fontSize: 24,
        fontWeight: '700',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 32,
    },
    createButton: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 4,
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    createButtonText: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
        paddingVertical: 16,
        paddingHorizontal: 32,
    },
    tabContainer: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    tab: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginHorizontal: 4,
        gap: 8,
    },
    tabText: {
        fontSize: 14,
        fontWeight: '500',
    },
    comingSoonContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    comingSoonTitle: {
        fontSize: 24,
        fontWeight: '700',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    comingSoonSubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    errorTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    errorSubtitle: {
        fontSize: 14,
        textAlign: 'center',
        lineHeight: 20,
        marginBottom: 24,
    },
    retryButton: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    retryButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
});
