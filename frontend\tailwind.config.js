/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './app/**/*.{js,jsx,ts,tsx}',
        './components/**/*.{js,jsx,ts,tsx}',
    ],
    presets: [require('nativewind/preset')],
    theme: {
        // Do NOT override the whole colors object, just extend it!
        extend: {
            colors: {
                white: '#fff',
                background: '#F4F7FA',
                primary: '#2A8E9E',
                secondary: '#a9d6e5',
                graytext: '#666666',
                purple: '#7C3AED',
                pink: '#EC4899',
            },
            boxShadow: {
                fab: '0px 2px 10px rgba(0, 0, 0, 0.25)',
            },
        },
    },
    plugins: [],
};
