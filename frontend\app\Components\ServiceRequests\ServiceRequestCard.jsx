import React, { useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';
import { router } from 'expo-router';

export default function ServiceRequestCard({ serviceRequest, onPress }) {
    const { theme } = useContext(ThemeContext);

    const formatDate = (dateString) => {
        if (!dateString) return 'Not specified';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget)) {
            return 'Budget not specified';
        }
        
        const formatAmount = (amount) => {
            if (amount >= 10000000) {
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            } else if (amount >= 100000) {
                return `₹${(amount / 100000).toFixed(1)}L`;
            } else if (amount >= 1000) {
                return `₹${(amount / 1000).toFixed(1)}K`;
            }
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        } else if (budget.minBudget) {
            return `From ${formatAmount(budget.minBudget)}`;
        } else if (budget.maxBudget) {
            return `Up to ${formatAmount(budget.maxBudget)}`;
        }
    };

    const formatLocation = (location) => {
        if (!location) return 'Location not specified';
        const parts = [];
        if (location.city) parts.push(location.city);
        if (location.state) parts.push(location.state);
        return parts.join(', ') || 'Location not specified';
    };

    const handleCardPress = () => {
        if (onPress) {
            onPress(serviceRequest);
        } else {
            router.push({
                pathname: '/Profile/ServiceRequestDetails',
                params: {
                    serviceRequestId: serviceRequest._id,
                    projectId: serviceRequest.projectId,
                }
            });
        }
    };

    return (
        <TouchableOpacity
            style={[styles.card, { backgroundColor: theme.CARD }]}
            onPress={handleCardPress}
            activeOpacity={0.8}
        >
            <View style={styles.cardHeader}>
                <View style={styles.projectInfo}>
                    <Text
                        style={[styles.projectName, { color: theme.TEXT_PRIMARY }]}
                        numberOfLines={1}
                    >
                        {serviceRequest.projectName}
                    </Text>
                    <Text
                        style={[styles.projectType, { color: theme.TEXT_SECONDARY }]}
                    >
                        {serviceRequest.projectType} • {serviceRequest.constructionType || 'Construction'}
                    </Text>
                </View>
                <View style={[styles.statusBadge, { backgroundColor: theme.PRIMARY + '20' }]}>
                    <Text style={[styles.statusText, { color: theme.PRIMARY }]}>
                        New
                    </Text>
                </View>
            </View>

            <View style={styles.cardBody}>
                <View style={styles.infoRow}>
                    <Ionicons name="location-outline" size={16} color={theme.TEXT_SECONDARY} />
                    <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
                        {formatLocation(serviceRequest.location)}
                    </Text>
                </View>

                <View style={styles.infoRow}>
                    <Ionicons name="wallet-outline" size={16} color={theme.TEXT_SECONDARY} />
                    <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
                        {formatBudget(serviceRequest.budget)}
                    </Text>
                </View>

                <View style={styles.infoRow}>
                    <Ionicons name="calendar-outline" size={16} color={theme.TEXT_SECONDARY} />
                    <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
                        Start: {formatDate(serviceRequest.expectedStartDate)}
                    </Text>
                </View>

                <View style={styles.infoRow}>
                    <Ionicons name="time-outline" size={16} color={theme.TEXT_SECONDARY} />
                    <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
                        Received: {formatDate(serviceRequest.createdAt)}
                    </Text>
                </View>
            </View>

            <View style={styles.cardFooter}>
                <View style={[styles.actionHint, { backgroundColor: theme.PRIMARY + '10' }]}>
                    <Text style={[styles.actionHintText, { color: theme.PRIMARY }]}>
                        Tap to view details and respond
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    card: {
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    projectInfo: {
        flex: 1,
        marginRight: 12,
    },
    projectName: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 4,
    },
    projectType: {
        fontSize: 14,
        fontWeight: '400',
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '600',
    },
    cardBody: {
        marginBottom: 12,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    infoText: {
        fontSize: 14,
        marginLeft: 8,
        flex: 1,
    },
    cardFooter: {
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.05)',
        paddingTop: 12,
    },
    actionHint: {
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        alignItems: 'center',
    },
    actionHintText: {
        fontSize: 12,
        fontWeight: '500',
    },
});
