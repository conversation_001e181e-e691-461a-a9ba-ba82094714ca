import React, { useContext } from 'react';
import {
    View,
    Modal,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    StyleSheet,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';

export default function OptionModal({
    OptionModalVisible,
    setOptionModalVisible,
}) {
    const { theme } = useContext(ThemeContext);
    return (
        <Modal
            transparent
            animationType="fade"
            visible={OptionModalVisible}
            onRequestClose={() => setOptionModalVisible(false)}
        >
            <TouchableWithoutFeedback
                onPress={() => setOptionModalVisible(false)}
            >
                <View
                    style={[
                        styles.modalOverlay,
                        { backgroundColor: theme.BLACK + '80' },
                    ]}
                >
                    <TouchableWithoutFeedback>
                        <View
                            style={[
                                styles.modalNearFAB,
                                {
                                    shadowColor: theme.SHADOW,
                                    backgroundColor: theme.CARD,
                                },
                            ]}
                        >
                            <TouchableOpacity style={styles.button}>
                                <MaterialIcons
                                    name="payments"
                                    size={20}
                                    color={theme.PRIMARY}
                                    style={styles.icon}
                                />
                                <Text
                                    style={[
                                        styles.text,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Payments
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.button}>
                                <Ionicons
                                    name="star-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                    style={styles.icon}
                                />
                                <Text
                                    style={[
                                        styles.text,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Starred
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.button}>
                                <Ionicons
                                    name="settings-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                    style={styles.icon}
                                />
                                <Text
                                    style={[
                                        styles.text,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Settings
                                </Text>
                            </TouchableOpacity>
                            {/* 
                            <TouchableOpacity style={styles.button}>
                                <LinearGradient
                                    colors={['#f78770', '#141516']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    style={styles.gradient}
                                />
                                <MaterialIcons
                                    name='engineering'
                                    size={20}
                                    color='#fff'
                                    style={styles.icon}
                                />
                                <Text style={styles.text}>Become Contractor</Text>
                            </TouchableOpacity> */}
                        </View>
                    </TouchableWithoutFeedback>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
}

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    modalNearFAB: {
        display: 'flex',
        top: 55,
        left: 90,
        right: 0,
        flexDirection: 'column',
        padding: 15,
        borderRadius: 16,
        shadowOpacity: 0.3,
        shadowRadius: 10,
        shadowOffset: { width: 0, height: 2 },
    },
    button: {
        display: 'flex',
        flexDirection: 'row',
        marginBottom: 5,
        padding: 5,
        alignItems: 'center',
        position: 'relative',
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    icon: {
        marginRight: 8,
    },
    text: {
        fontSize: 16,
        fontWeight: '500',
    },
});
