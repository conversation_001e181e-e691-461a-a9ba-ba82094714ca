// components/HeroSection.js
import React, { useState, useContext } from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import Ionicons from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';

export default function HeroSection({ onFindContractors }) {
    const { theme } = useContext(ThemeContext);
    const [query, setQuery] = useState('');

    return (
        <View style={[styles.hero, { backgroundColor: theme.WHITE }]}>
            {/* Search Bar */}
            <View style={styles.searchWrapper}>
                <Ionicons
                    name="search-outline"
                    size={20}
                    color="gray"
                    style={styles.searchIcon}
                />
                <TextInput
                    style={styles.searchInput}
                    placeholder="Find a Contractor or Land Near You"
                    value={query}
                    onChangeText={setQuery}
                    returnKeyType="search"
                    onSubmitEditing={() => {
                        if (query.trim()) {
                            onFindContractors && onFindContractors(query);
                        }
                    }}
                />
            </View>

            {/* Action Buttons */}
            <View style={styles.buttons}>
                {/* <TouchableOpacity
          style={[styles.button, styles.contractorsBtn,backgroundColor: theme.PRIMARY,]}
          onPress={() => onFindContractors && onFindContractors(query)}
        >
          <Text style={styles.buttonText}>Find Contractors</Text>
        </TouchableOpacity> */}

                {/* <TouchableOpacity
          style={[styles.button, styles.landBtn,backgroundColor: theme.PRIMARY,]}
          onPress={() => onFindLand && onFindLand(query)}
        >
          <Text style={styles.buttonText}>Find Land</Text>
        </TouchableOpacity> */}
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    hero: {
        padding: 16,
    },
    searchWrapper: {
        position: 'relative',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f1f1f1',
        borderRadius: 20,
        paddingHorizontal: 12,
        marginBottom: 16,
    },
    searchIcon: {
        marginRight: 8,
    },
    searchInput: {
        flex: 1,
        height: 40,
        fontSize: 16,
        color: '#333',
    },
    buttons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    button: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    contractorsBtn: {
        marginRight: 8,
    },
    landBtn: {
        marginLeft: 8,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});
