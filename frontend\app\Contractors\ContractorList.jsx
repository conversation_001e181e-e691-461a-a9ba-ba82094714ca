import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    TextInput,
    Image,
    SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchAllContractors } from '../../api/contractor/contractorApi';

const ContractorList = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [isSearchBarVisible, setSearchBarVisible] = useState(false);

    const { data: contractors } = useQuery({
        queryKey: ['allContractors'],
        queryFn: fetchAllContractors,
    });

    const categories = [
        { id: 'all', name: 'All', icon: 'grid' },
        { id: 'Building', name: 'Building', icon: 'business' },
        { id: 'Architecture', name: 'Architecture', icon: 'library' },
        { id: 'Maintenance', name: 'Maintenance', icon: 'construct' },
        { id: 'Interior', name: 'Interior', icon: 'color-palette' },
    ];

    const filteredContractors = contractors.filter((contractor) => {
        const matchesSearch =
            (contractor.name?.toLowerCase() || '').includes(
                searchQuery.toLowerCase()
            ) ||
            (contractor.serviceAreas?.join(' ')?.toLowerCase() || '').includes(
                searchQuery.toLowerCase()
            );

        const matchesCategory =
            selectedCategory === 'all' ||
            contractor.category === selectedCategory;

        return matchesSearch && matchesCategory;
    });

    const renderContractor = (contractor) => (
        <TouchableOpacity
            key={contractor.id}
            style={[
                styles.contractorCard,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={() =>
                router.push(
                    `/Profile/ContractorProfile?contractorId=${contractor.id}`
                )
            }
        >
            <View style={styles.contractorHeader}>
                {/* Profile Image and Service Areas */}
                <View style={styles.profileColumn}>
                    {contractor.isAvailable ? (
                        <View style={styles.available}>
                            <View
                                style={[
                                    styles.availableDot,
                                    { backgroundColor: theme.SUCCESS },
                                ]}
                            />
                        </View>
                    ) : (
                        <View style={styles.available}>
                            <View
                                style={[
                                    styles.availableDot,
                                    { backgroundColor: theme.ERROR },
                                ]}
                            />
                        </View>
                    )}
                    {contractor.image ? (
                        <Image
                            source={{ uri: contractor.image }}
                            style={styles.contractorImage}
                        />
                    ) : (
                        <View
                            style={[
                                styles.contractorImage,
                                { backgroundColor: theme.GRAY_LIGHT + 'CC' },
                            ]}
                        >
                            <Ionicons
                                name="person"
                                size={50}
                                color={theme.TEXT_SECONDARY}
                            />
                        </View>
                    )}

                    {/* Service Areas */}
                    <View style={styles.serviceAreasContainer}>
                        {contractor.serviceAreas
                            .slice(0, 1)
                            .map((area, index) => (
                                <View
                                    key={index}
                                    style={[
                                        styles.specialtyTag,
                                        { borderColor: theme.PRIMARY },
                                    ]}
                                >
                                    <Ionicons
                                        name="location-outline"
                                        size={14}
                                        color={theme.PRIMARY}
                                        style={{ marginRight: 4 }}
                                    />
                                    <Text
                                        style={[
                                            styles.specialtyText,
                                            {
                                                color: theme.PRIMARY,
                                                flexShrink: 1,
                                            },
                                        ]}
                                    >
                                        {area}
                                    </Text>
                                </View>
                            ))}
                        {contractor.serviceAreas.length > 1 && (
                            <View
                                style={[
                                    styles.specialtyTag,
                                    { borderColor: 'transparent' },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.specialtyText,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    +{contractor.serviceAreas.length - 1}
                                </Text>
                            </View>
                        )}
                    </View>
                </View>

                {/* Name, Rating and Experience */}
                <View style={styles.contractorInfo}>
                    <View style={styles.nameExperienceRow}>
                        {/* Name and Rating */}
                        <View style={{ flex: 1 }}>
                            <Text
                                style={[
                                    styles.contractorName,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {contractor.name}
                            </Text>
                            <View style={styles.ratingRow}>
                                <Ionicons
                                    name="star"
                                    size={16}
                                    color="#FFD700"
                                />
                                <Text
                                    style={[
                                        styles.rating,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {contractor.ratings}
                                </Text>
                            </View>
                        </View>

                        {/* Experience */}
                        <View
                            style={{
                                flexDirection: 'column',
                                alignItems: 'flex-end',
                                justifyContent: 'center',
                            }}
                        >
                            <Text
                                style={[
                                    styles.contractorCategory,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                {contractor.experience} years
                            </Text>
                            <Text
                                style={[
                                    styles.contractorCategory,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                experience
                            </Text>
                        </View>
                    </View>

                    {/* Specialties - full width and wraps properly */}
                    <View style={styles.specialtiesFullWidth}>
                        {contractor.specialties
                            .slice(0, 1)
                            .map((specialty, index) => (
                                <View
                                    key={index}
                                    style={[
                                        styles.specialtyTag,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                            borderColor: 'transparent',
                                            borderRadius: 10,
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.specialtyText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        {specialty}
                                    </Text>
                                </View>
                            ))}
                        {contractor.specialties.length > 1 && (
                            <View
                                style={[
                                    styles.specialtyTag,
                                    {
                                        borderColor: 'transparent',
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.specialtyText,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    +{contractor.specialties.length - 1}
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Find Contractors</Text>
                    <TouchableOpacity
                        onPress={() =>
                            router.push('/Contractors/ContractorFilters')
                        }
                    >
                        <Ionicons name="filter" size={24} color="#fff" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => setSearchBarVisible(!isSearchBarVisible)}
                    >
                        <Ionicons name="search" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            {isSearchBarVisible && (
                /* Search Bar */
                <View
                    style={[
                        styles.searchContainer,
                        {
                            backgroundColor: theme.CARD,
                            shadowColor: theme.PRIMARY,
                        },
                    ]}
                >
                    <Ionicons
                        name="search"
                        size={20}
                        color={theme.TEXT_SECONDARY}
                    />
                    <TextInput
                        style={[
                            styles.searchInput,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                        placeholder="Search contractors..."
                        placeholderTextColor={theme.TEXT_SECONDARY}
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                    />
                </View>
            )}
            <View style={styles.categoriesContainer}>
                {/* Categories */}
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.categoriesContent}
                >
                    {categories.map((category) => (
                        <TouchableOpacity
                            key={category.id}
                            style={[
                                styles.categoryButton,
                                {
                                    backgroundColor:
                                        selectedCategory === category.id
                                            ? theme.PRIMARY + '66'
                                            : theme.BACKGROUND,
                                },
                                {
                                    borderColor:
                                        selectedCategory === category.id
                                            ? theme.PRIMARY
                                            : theme.GRAY_LIGHT,
                                },
                            ]}
                            onPress={() => setSelectedCategory(category.id)}
                        >
                            <Ionicons
                                name={category.icon}
                                size={18}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.categoryText,
                                    {
                                        color:
                                            selectedCategory === category.id
                                                ? theme.PRIMARY
                                                : theme.TEXT_SECONDARY,
                                    },
                                ]}
                            >
                                {category.name}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </View>

            {/* Contractors List */}
            {filteredContractors.length === 0 ? (
                <View style={styles.emptyContainer}>
                    <Ionicons
                        name="people-outline"
                        size={64}
                        color={theme.TEXT_PLACEHOLDER}
                    />
                    <Text
                        style={[
                            styles.emptyText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        No contractors found
                    </Text>
                </View>
            ) : (
                <ScrollView
                    style={styles.contractorsList}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={styles.contractorsContent}
                >
                    {filteredContractors.map(renderContractor)}
                </ScrollView>
            )}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 10,
        paddingBottom: 10,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#fff',
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: 10,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 12,
        elevation: 3,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    searchInput: {
        marginLeft: 10,
        fontSize: 14,
    },
    categoriesContainer: {
        marginTop: 4,
        marginBottom: 6,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 4,
    },
    emptyText: {
        fontSize: 16,
        marginTop: 16,
    },
    categoriesContent: {
        paddingHorizontal: 10,
        alignItems: 'center',
    },
    categoryButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 25,
        marginRight: 8,
        borderWidth: 1,
    },
    categoryText: {
        marginLeft: 8,
        fontSize: 12,
        fontWeight: '600',
    },
    contractorsList: {
        flex: 1,
        marginTop: 4,
    },
    profileColumn: {
        width: '42%',
        alignItems: 'center',
        justifyContent: 'flex-start',
        gap: 6,
    },
    serviceAreasContainer: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    nameExperienceRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 6,
    },
    specialtiesFullWidth: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginTop: 6,
    },
    contractorsContent: {
        paddingHorizontal: 15,
        paddingBottom: 10,
    },
    contractorCard: {
        padding: 12,
        borderRadius: 12,
        marginBottom: 8,
        elevation: 3,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    contractorHeader: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    contractorImage: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 80,
        height: 80,
        borderRadius: 40,
        marginRight: 12,
    },
    contractorInfo: {
        flex: 1,
    },
    contractorName: {
        fontSize: 16,
        fontWeight: 'bold',
        marginRight: 8,
    },
    contractorCategory: {
        fontSize: 10,
        marginBottom: 4,
        fontWeight: '500',
    },
    ratingRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 4,
    },
    location: {
        fontSize: 14,
        marginLeft: 4,
    },
    available: {
        position: 'absolute',
        top: 60,
        left: 75,
        padding: 4,
        fontWeight: '600',
        zIndex: 1,
    },
    availableDot: {
        width: 10,
        height: 10,
        borderRadius: 10,
    },
    specialtyTag: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        justifyContent: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 30,
        borderWidth: 1,
        maxWidth: '90%',
    },

    specialtyText: {
        fontSize: 12,
        fontWeight: '600',
    },
});

export default ContractorList;
