import { publicAPIClient, privateAPIClient } from '../index';
import * as SecureStore from 'expo-secure-store';

// Login user
export const loginUser = async (credentials) => {
    const response = await publicAPIClient.post('/auth/login', credentials);

    if (response.data.token) {
        await SecureStore.setItemAsync('accessToken', response.data.token);
        if (response.data.sessionId) {
            await SecureStore.setItemAsync(
                'sessionId',
                response.data.sessionId
            );
        }
    }

    return response.data;
};

// Register user
export const registerUser = async (userData) => {
    const response = await publicAPIClient.post('/auth/register', userData);
    return response.data;
};

// Logout user
export const logoutUser = async () => {
    try {
        await privateAPIClient.post('/auth/logout');
    } catch (error) {
        console.error('Logout API error:', error);
    } finally {
        await SecureStore.deleteItemAsync('accessToken');
        await SecureStore.deleteItemAsync('sessionId');
    }
    return { success: true };
};

// Get current user profile
export const getCurrentUser = async () => {
    const response = await privateAPIClient.get('/user-service/api/v1/user/profile');
    return response.data.user;
};
