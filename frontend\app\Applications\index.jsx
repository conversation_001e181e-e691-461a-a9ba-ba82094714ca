import React, { useEffect, useState, useContext, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StatusBar,
    Image,
    FlatList,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { fetchUserProfile } from '../../api/user/userApi';
import { fetchBrokerProfile } from '../../api/broker/brokerApi';
import { fetchContractorProfile } from '../../api/contractor/contractorApi';
import BackButton from '../Components/Shared/BackButton';
import DocumentPreviewModal from '../Components/Profile/DocumentPreviewModal';
import ApplicationDetailsModal from './ApplicationDetailsModal';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';
import ApplicationSection from './ApplicationSection';
import SkeletonLoader from './SkeletonLoader';
import styles from './styles';

export default function Applications() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const queryClient = useQueryClient();
    const [applications, setApplications] = useState({
        brokers: [],
        requests: [],
        contractors: [],
    });
    const [isFetching, setIsFetching] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [detailsModalVisible, setDetailsModalVisible] = useState(false);
    const [modalDocument, setModalDocument] = useState({ url: '', type: '' });
    const [selectedApplication, setSelectedApplication] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [partnershipRequest, setPartnershipRequest] = useState('none');
    const flatListRef = useRef(null);

    // Fetch user profile
    const {
        data: user,
        isLoading: isUserLoading,
        error: userError,
        refetch: refetchUser,
    } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
        refetchInterval: false,
        refetchIntervalInBackground: false,
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch user profile.');
        },
    });

    // Fetch broker data only if user is loaded
    const {
        data: brokerData,
        isLoading: isBrokerLoading,
        error: brokerError,
    } = useQuery({
        queryKey: ['brokerApplication'],
        queryFn: fetchBrokerProfile,
        enabled: !!user,
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch broker data.');
        },
    });

    // Fetch contractor data only if user is loaded
    const {
        data: contractorData,
        isLoading: isContractorLoading,
        error: contractorError,
    } = useQuery({
        queryKey: ['contractorApplication'],
        queryFn: fetchContractorProfile,
        enabled: !!user,
        refetchInterval: false,
        refetchIntervalInBackground: false,
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch contractor data.');
        },
    });

    useEffect(() => {
        if (!isUserLoading && user) {
            const role = user.role.toLowerCase();
            let determinedType = '';
            const filteredApplications = {
                brokers: [],
                contractors: [],
                requests: [],
            };

            if (brokerData && !isBrokerLoading) {
                const brokerApps = Array.isArray(brokerData)
                    ? brokerData
                    : [brokerData];
                determinedType = 'broker';
                filteredApplications.brokers = brokerApps.map((app) => ({
                    ...app,
                    id: app.brokerId || app.id,
                    aadhaarDocument: app.aadhaarAsset,
                    panDocument: app.panAsset,
                    status: app.status || 'pending',
                    type: 'brokers',
                }));
            }

            if (contractorData && !isContractorLoading) {
                const contractorApps = Array.isArray(contractorData)
                    ? contractorData
                    : [contractorData];
                determinedType = 'contractor';
                filteredApplications.contractors = contractorApps.map(
                    (app) => ({
                        ...app,
                        id: app.contractorId || app.id,
                        aadhaarDocument: app.aadhaarAsset,
                        panDocument: app.panAsset,
                        status: app.status || 'pending',
                        type: 'contractors',
                    })
                );
            }

            setPartnershipRequest(determinedType);
            setApplications(filteredApplications);

            // Set isFetching based on the relevant query
            const isRelevantQueryLoading =
                (role === 'user' &&
                    determinedType === 'none' &&
                    isBrokerLoading &&
                    !brokerData &&
                    !brokerError &&
                    isContractorLoading &&
                    !contractorData &&
                    !contractorError) ||
                (determinedType === 'broker' &&
                    isBrokerLoading &&
                    !brokerData &&
                    !brokerError) ||
                (determinedType === 'contractor' &&
                    isContractorLoading &&
                    !contractorData &&
                    !contractorError);
            setIsFetching(isRelevantQueryLoading);
        }
    }, [
        isUserLoading,
        user,
        isBrokerLoading,
        brokerData,
        brokerError,
        isContractorLoading,
        contractorData,
        contractorError,
    ]);

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await Promise.all([
                queryClient.invalidateQueries({ queryKey: ['userProfile'] }),
                partnershipRequest === 'Broker' ||
                user?.role.toLowerCase() === 'broker'
                    ? queryClient.invalidateQueries({
                          queryKey: ['brokerApplication'],
                      })
                    : Promise.resolve(),
                partnershipRequest === 'Contractor' ||
                user?.role.toLowerCase() === 'contractor'
                    ? queryClient.invalidateQueries({
                          queryKey: ['contractorApplication'],
                      })
                    : Promise.resolve(),
            ]);
        } catch (error) {
            showToast('error', 'Error', 'Failed to refresh data.');
        } finally {
            setRefreshing(false);
        }
    };

    const handlePreview = (url, documentType) => {
        if (url && url.startsWith('http')) {
            setModalDocument({ url, type: documentType });
            setModalVisible(true);
        } else {
            showToast(
                'error',
                'Error',
                `${documentType} document not available or invalid.`
            );
        }
    };

    const handleEdit = (data) => {
        if (data.type === 'contractors') {
            router.replace({
                pathname: '/Contractors/ContractorForm',
                params: { editData: JSON.stringify(data) },
            });
        } else if (data.type === 'brokers') {
            router.replace({
                pathname: '/Broker/BrokerForm',
                params: { editData: JSON.stringify(data) },
            });
        }
    };

    const renderHeader = () => (
        <>
            <View style={styles.headerContainer}>
                <BackButton
                    color={theme.WHITE}
                    onPress={() => router.replace('/Profile')}
                />
                <Text style={[styles.headerTitle, { color: theme.WHITE }]}>
                    Applications
                </Text>
            </View>
            <View style={styles.backgroundOverlay}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                    style={styles.gradientOverlay}
                />
            </View>
        </>
    );

    const renderItem = ({ item }) => {
        if (item.type === 'no-data') {
            return (
                <View
                    style={[
                        styles.cardContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <Text
                        style={[
                            styles.noDataText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {role === 'user'
                            ? 'You have not submitted any applications.'
                            : 'No requests available.'}
                    </Text>
                </View>
            );
        }

        return (
            <ApplicationSection
                title={item.title}
                applications={item.data}
                theme={theme}
                handleViewDetails={(data) => {
                    setSelectedApplication(data);
                    setDetailsModalVisible(true);
                }}
                handleEdit={handleEdit}
                isRequests={item.isRequests}
            />
        );
    };

    if (isFetching || userError) {
        return (
            <View
                style={[
                    styles.loadingContainer,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                />
                {userError ? (
                    <View style={styles.errorContainer}>
                        <Text
                            style={[
                                styles.loadingText,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Failed to load user data. Please try again.
                        </Text>
                        <TouchableOpacity
                            style={styles.retryButton}
                            onPress={refetchUser}
                            accessibilityLabel="Retry loading data"
                            accessibilityRole="button"
                        >
                            <Text
                                style={[
                                    styles.retryButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Retry
                            </Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    <SkeletonLoader theme={theme} />
                )}
            </View>
        );
    }

    const data = [];
    const role = user?.role.toLowerCase();
    if (role === 'user') {
        if (user.partnershipRequest === 'Broker') {
            data.push({
                type: 'section',
                title: 'Your Submitted Site Scout Application',
                id: 'broker-section',
                data: applications.brokers,
                isRequests: false,
            });
        } else if (user.partnershipRequest === 'Contractor') {
            data.push({
                type: 'section',
                title: 'Your Submitted Contractor Application',
                id: 'contractor-section',
                data: applications.contractors,
                isRequests: false,
            });
        }
        if (
            applications.brokers.length === 0 &&
            applications.contractors.length === 0
        ) {
            data.push({ type: 'no-data', id: 'no-data' });
        }
    } else if (role === 'broker' || role === 'contractor') {
        if (applications.requests.length > 0) {
            data.push({
                type: 'section',
                title:
                    role === 'broker'
                        ? 'Site Verification Requests'
                        : 'Construction Requests',
                id: 'requests-section',
                data: applications.requests,
                isRequests: true,
            });
        } else {
            data.push({ type: 'no-data', id: 'no-data' });
        }
    }

    return (
        <View style={[styles.safe, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />
            <FlatList
                ref={flatListRef}
                data={data}
                renderItem={renderItem}
                keyExtractor={(item) => item.id}
                ListHeaderComponent={renderHeader}
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                windowSize={5}

            />
            <DocumentPreviewModal
                visible={modalVisible}
                documentUrl={modalDocument.url}
                documentType={modalDocument.type}
                onClose={() => setModalVisible(false)}
            />
            <ApplicationDetailsModal
                visible={detailsModalVisible}
                onClose={() => setDetailsModalVisible(false)}
                data={selectedApplication}
                onPreview={handlePreview}
            />
        </View>
    );
}
